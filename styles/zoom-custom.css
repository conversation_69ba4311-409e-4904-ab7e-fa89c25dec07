/* Custom styles for react-medium-image-zoom */

/* Semi-transparent background overlay */
[data-rmiz-modal-overlay] {
  background-color: rgba(0, 0, 0, 0.6) !important; /* Semi-transparent black */
}

/* Custom transition duration */
[data-rmiz-modal-overlay] {
  transition: background-color 0.3s ease-in-out !important;
}

/* Custom close button styling */
[data-rmiz-btn-unzoom] {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

[data-rmiz-btn-unzoom]:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Custom image styling in zoom - full viewport sizing */
[data-rmiz-modal-img] {
  border-radius: 8px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.2) !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
}

/* Custom dialog styling using the classDialog prop */
.custom-zoom-dialog {
  backdrop-filter: blur(4px) !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure the modal content container takes full space */
[data-rmiz-modal-content] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
